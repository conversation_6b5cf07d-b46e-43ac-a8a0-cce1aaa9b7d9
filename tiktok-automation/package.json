{"name": "tiktok-automation-desktop", "version": "1.0.0", "description": "TikTok Automation Desktop App with Antidetect Browser", "main": "src/main/main.js", "homepage": "./", "scripts": {"start": "concurrently \"npm run start:backend\" \"npm run start:electron\"", "start:backend": "cd backend && python -m uvicorn main:app --reload --port 8000", "start:electron": "wait-on http://localhost:8000 && electron .", "dev": "concurrently \"npm run start:backend\" \"npm run dev:electron\"", "dev:electron": "wait-on http://localhost:8000 && electron . --dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && python -m PyInstaller --onefile main.py", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["tiktok", "automation", "antidetect", "browser", "desktop", "electron"], "author": "TikTok Automation Team", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "eslint": "^8.56.0", "prettier": "^3.1.1", "jest": "^29.7.0"}, "dependencies": {"electron-store": "^8.1.0", "electron-updater": "^6.1.7", "axios": "^1.6.2", "ws": "^8.16.0"}, "build": {"appId": "com.tiktokautomation.desktop", "productName": "TikTok Automation", "directories": {"output": "dist"}, "files": ["src/**/*", "frontend/build/**/*", "backend/dist/**/*", "node_modules/**/*"], "extraResources": [{"from": "backend/dist/", "to": "backend/"}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}