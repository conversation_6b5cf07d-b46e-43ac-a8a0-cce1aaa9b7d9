/**
 * Recent Activity Component for Dashboard
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiUser,
  FiUserPlus,
  FiUserMinus,
  FiPlay,
  FiPause,
  FiSettings,
  FiGlobe,
  FiClock
} from 'react-icons/fi';

const RecentActivity = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock activity data - in real app this would come from API
  useEffect(() => {
    const mockActivities = [
      {
        id: 1,
        type: 'task_started',
        title: 'Task Started',
        description: 'Follow task for @competitor1 started',
        timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        icon: FiPlay,
        color: 'green'
      },
      {
        id: 2,
        type: 'user_followed',
        title: 'User Followed',
        description: 'Successfully followed @user123',
        timestamp: new Date(Date.now() - 12 * 60 * 1000), // 12 minutes ago
        icon: FiUserPlus,
        color: 'blue'
      },
      {
        id: 3,
        type: 'profile_created',
        title: 'Profile Created',
        description: 'New browser profile "Profile 5" created',
        timestamp: new Date(Date.now() - 25 * 60 * 1000), // 25 minutes ago
        icon: FiUser,
        color: 'purple'
      },
      {
        id: 4,
        type: 'proxy_added',
        title: 'Proxy Added',
        description: 'New proxy server ************* configured',
        timestamp: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
        icon: FiGlobe,
        color: 'orange'
      },
      {
        id: 5,
        type: 'task_paused',
        title: 'Task Paused',
        description: 'Unfollow task paused due to rate limit',
        timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
        icon: FiPause,
        color: 'yellow'
      },
      {
        id: 6,
        type: 'user_unfollowed',
        title: 'User Unfollowed',
        description: 'Unfollowed @inactive_user456',
        timestamp: new Date(Date.now() - 75 * 60 * 1000), // 1 hour 15 minutes ago
        icon: FiUserMinus,
        color: 'red'
      },
      {
        id: 7,
        type: 'settings_updated',
        title: 'Settings Updated',
        description: 'Rate limit settings modified',
        timestamp: new Date(Date.now() - 90 * 60 * 1000), // 1 hour 30 minutes ago
        icon: FiSettings,
        color: 'gray'
      }
    ];

    // Simulate loading delay
    setTimeout(() => {
      setActivities(mockActivities);
      setLoading(false);
    }, 1000);
  }, []);

  const getTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  const getColorClasses = (color) => {
    const colorMap = {
      green: 'bg-green-100 text-green-600',
      blue: 'bg-blue-100 text-blue-600',
      purple: 'bg-purple-100 text-purple-600',
      orange: 'bg-orange-100 text-orange-600',
      yellow: 'bg-yellow-100 text-yellow-600',
      red: 'bg-red-100 text-red-600',
      gray: 'bg-gray-100 text-gray-600'
    };
    return colorMap[color] || colorMap.gray;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="animate-pulse flex items-start space-x-3">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="h-3 bg-gray-200 rounded w-16"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
        <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
          View All
        </button>
      </div>

      {activities.length === 0 ? (
        <div className="text-center py-8">
          <FiClock className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Recent Activity</h3>
          <p className="text-gray-600">Activity will appear here as you use the system.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {activities.map((activity, index) => {
            const Icon = activity.icon;
            return (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getColorClasses(activity.color)}`}>
                  <Icon className="w-4 h-4" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900 truncate">
                      {activity.title}
                    </h3>
                    <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                      {getTimeAgo(activity.timestamp)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1 truncate">
                    {activity.description}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>
      )}

      {/* Activity Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {activities.filter(a => a.type.includes('task')).length}
            </div>
            <div className="text-sm text-gray-600">Tasks</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {activities.filter(a => a.type.includes('user')).length}
            </div>
            <div className="text-sm text-gray-600">User Actions</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {activities.filter(a => a.type.includes('profile') || a.type.includes('proxy')).length}
            </div>
            <div className="text-sm text-gray-600">Config Changes</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecentActivity;
