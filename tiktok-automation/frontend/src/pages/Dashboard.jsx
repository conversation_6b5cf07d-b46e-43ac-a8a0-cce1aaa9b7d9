/**
 * Dashboard Page - Main overview page
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useOutletContext } from 'react-router-dom';
import {
  FiUsers,
  FiGlobe,
  FiPlay,
  FiTarget,
  FiTrendingUp,
  FiActivity,
  FiClock,
  FiCheckCircle
} from 'react-icons/fi';

// Components
import StatsCard from '../components/Dashboard/StatsCard';
import TasksOverview from '../components/Dashboard/TasksOverview';
import RecentActivity from '../components/Dashboard/RecentActivity';
import SystemStatus from '../components/Dashboard/SystemStatus';

const Dashboard = () => {
  const { setPageTitle, setPageSubtitle } = useOutletContext();
  const [stats, setStats] = useState({
    profiles: { total: 0, active: 0 },
    proxies: { total: 0, active: 0 },
    accounts: { total: 0, logged_in: 0 },
    tasks: { total: 0, running: 0, completed: 0 }
  });
  const [loading, setLoading] = useState(true);

  // Set page title
  useEffect(() => {
    setPageTitle('Dashboard');
    setPageSubtitle('Overview of your TikTok automation system');
  }, [setPageTitle, setPageSubtitle]);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Get backend URL
        let baseUrl = 'http://localhost:8000';
        if (window.electronAPI) {
          baseUrl = await window.electronAPI.getBackendUrl();
        }

        // Fetch all stats in parallel
        const [profilesRes, proxiesRes, accountsRes, tasksRes] = await Promise.all([
          fetch(`${baseUrl}/api/v1/profiles/`),
          fetch(`${baseUrl}/api/v1/proxies/`),
          fetch(`${baseUrl}/api/v1/accounts/`),
          fetch(`${baseUrl}/api/v1/tasks/`)
        ]);

        const [profiles, proxies, accounts, tasks] = await Promise.all([
          profilesRes.json(),
          proxiesRes.json(),
          accountsRes.json(),
          tasksRes.json()
        ]);

        // Calculate stats
        setStats({
          profiles: {
            total: profiles.length,
            active: profiles.filter(p => p.is_active).length
          },
          proxies: {
            total: proxies.length,
            active: proxies.filter(p => p.status === 'active').length
          },
          accounts: {
            total: accounts.length,
            logged_in: accounts.filter(a => a.is_logged_in).length
          },
          tasks: {
            total: tasks.length,
            running: tasks.filter(t => t.status === 'running').length,
            completed: tasks.filter(t => t.status === 'completed').length
          }
        });

      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
    
    // Refresh data every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const statsCards = [
    {
      title: 'Browser Profiles',
      value: stats.profiles.total,
      subtitle: `${stats.profiles.active} active`,
      icon: FiUsers,
      color: 'blue',
      trend: '+12%',
      trendUp: true
    },
    {
      title: 'Proxies',
      value: stats.proxies.total,
      subtitle: `${stats.proxies.active} online`,
      icon: FiGlobe,
      color: 'green',
      trend: '+5%',
      trendUp: true
    },
    {
      title: 'TikTok Accounts',
      value: stats.accounts.total,
      subtitle: `${stats.accounts.logged_in} logged in`,
      icon: FiTarget,
      color: 'purple',
      trend: '+8%',
      trendUp: true
    },
    {
      title: 'Active Tasks',
      value: stats.tasks.running,
      subtitle: `${stats.tasks.completed} completed today`,
      icon: FiPlay,
      color: 'orange',
      trend: '+15%',
      trendUp: true
    }
  ];

  const quickActions = [
    {
      title: 'Create Profile',
      description: 'Set up a new browser profile',
      icon: FiUsers,
      color: 'blue',
      action: () => window.location.href = '/profiles/new'
    },
    {
      title: 'Add Proxy',
      description: 'Configure a new proxy server',
      icon: FiGlobe,
      color: 'green',
      action: () => window.location.href = '/proxies/new'
    },
    {
      title: 'New Task',
      description: 'Start an automation task',
      icon: FiPlay,
      color: 'purple',
      action: () => window.location.href = '/tasks/new'
    },
    {
      title: 'View Analytics',
      description: 'Check performance metrics',
      icon: FiTrendingUp,
      color: 'orange',
      action: () => window.location.href = '/analytics'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((card, index) => (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <StatsCard {...card} />
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <button
                key={action.title}
                onClick={action.action}
                className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200 text-left group"
              >
                <div className={`w-10 h-10 bg-${action.color}-100 text-${action.color}-600 rounded-lg flex items-center justify-center mb-3 group-hover:bg-${action.color}-200 transition-colors`}>
                  <Icon className="w-5 h-5" />
                </div>
                <h3 className="font-medium text-gray-900 mb-1">{action.title}</h3>
                <p className="text-sm text-gray-600">{action.description}</p>
              </button>
            );
          })}
        </div>
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Tasks Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="lg:col-span-2"
        >
          <TasksOverview />
        </motion.div>

        {/* System Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <SystemStatus />
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <RecentActivity />
      </motion.div>
    </div>
  );
};

export default Dashboard;
