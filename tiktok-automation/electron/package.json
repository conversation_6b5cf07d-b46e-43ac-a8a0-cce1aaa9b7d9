{"name": "tiktok-automation-electron", "version": "1.0.0", "description": "TikTok Automation Desktop Application", "main": "main.js", "author": "TikTok Automation Team", "license": "MIT", "private": true, "homepage": "./", "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"electron-is-dev": "^2.0.0", "electron-updater": "^6.1.7", "electron-window-state": "^5.0.3", "node-fetch": "^2.7.0"}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^28.0.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.tiktokautomation.app", "productName": "TikTok Automation", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "assets/**/*", "../frontend/build/**/*", "../backend/**/*", "!../backend/__pycache__/**/*", "!../backend/.pytest_cache/**/*", "!../backend/venv/**/*", "!../backend/.env*"], "extraResources": [{"from": "../backend", "to": "backend", "filter": ["**/*", "!__pycache__/**/*", "!.pytest_cache/**/*", "!venv/**/*", "!.env*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.productivity", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "TikTok Automation"}, "dmg": {"title": "TikTok Automation", "icon": "assets/icon.icns", "background": "assets/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "publish": {"provider": "github", "owner": "your-username", "repo": "tiktok-automation"}}}