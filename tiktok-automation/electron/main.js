/**
 * TikTok Automation - Electron Main Process
 * Handles window management, system integration, and backend communication
 */

const { app, BrowserWindow, ipcMain, Menu, Tray, dialog, shell, protocol } = require('electron');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const isDev = require('electron-is-dev');
const { autoUpdater } = require('electron-updater');
const windowStateKeeper = require('electron-window-state');

// Keep a global reference of the window object
let mainWindow;
let tray;
let backendProcess;
let isQuitting = false;

// Backend configuration
const BACKEND_PORT = 8000;
const BACKEND_HOST = '127.0.0.1';
const BACKEND_URL = `http://${BACKEND_HOST}:${BACKEND_PORT}`;

/**
 * Create the main application window
 */
function createMainWindow() {
    // Load window state
    let mainWindowState = windowStateKeeper({
        defaultWidth: 1400,
        defaultHeight: 900
    });

    // Create the browser window
    mainWindow = new BrowserWindow({
        x: mainWindowState.x,
        y: mainWindowState.y,
        width: mainWindowState.width,
        height: mainWindowState.height,
        minWidth: 1200,
        minHeight: 800,
        show: false, // Don't show until ready
        icon: getIconPath(),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js'),
            webSecurity: !isDev
        },
        titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
        frame: process.platform !== 'darwin',
        backgroundColor: '#1a1a1a'
    });

    // Let windowStateKeeper manage the window
    mainWindowState.manage(mainWindow);

    // Load the app
    if (isDev) {
        // Development: load from React dev server
        mainWindow.loadURL('http://localhost:3000');
        mainWindow.webContents.openDevTools();
    } else {
        // Production: load from built files
        mainWindow.loadFile(path.join(__dirname, '../frontend/build/index.html'));
    }

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // Focus on window
        if (isDev) {
            mainWindow.focus();
        }
    });

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Handle window close event
    mainWindow.on('close', (event) => {
        if (!isQuitting && process.platform === 'darwin') {
            event.preventDefault();
            mainWindow.hide();
        } else if (!isQuitting) {
            event.preventDefault();
            mainWindow.hide();
        }
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // Setup menu
    setupMenu();

    // Setup system tray
    setupTray();

    return mainWindow;
}

/**
 * Setup application menu
 */
function setupMenu() {
    const template = [
        {
            label: 'File',
            submenu: [
                {
                    label: 'New Profile',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.webContents.send('menu-action', 'new-profile');
                    }
                },
                {
                    label: 'Import Profiles',
                    click: async () => {
                        const result = await dialog.showOpenDialog(mainWindow, {
                            properties: ['openFile'],
                            filters: [
                                { name: 'JSON Files', extensions: ['json'] },
                                { name: 'All Files', extensions: ['*'] }
                            ]
                        });
                        
                        if (!result.canceled) {
                            mainWindow.webContents.send('menu-action', 'import-profiles', result.filePaths[0]);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Settings',
                    accelerator: 'CmdOrCtrl+,',
                    click: () => {
                        mainWindow.webContents.send('menu-action', 'settings');
                    }
                },
                { type: 'separator' },
                {
                    label: 'Quit',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        isQuitting = true;
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'Edit',
            submenu: [
                { role: 'undo' },
                { role: 'redo' },
                { type: 'separator' },
                { role: 'cut' },
                { role: 'copy' },
                { role: 'paste' },
                { role: 'selectall' }
            ]
        },
        {
            label: 'View',
            submenu: [
                { role: 'reload' },
                { role: 'forceReload' },
                { role: 'toggleDevTools' },
                { type: 'separator' },
                { role: 'resetZoom' },
                { role: 'zoomIn' },
                { role: 'zoomOut' },
                { type: 'separator' },
                { role: 'togglefullscreen' }
            ]
        },
        {
            label: 'Automation',
            submenu: [
                {
                    label: 'Start All Tasks',
                    click: () => {
                        mainWindow.webContents.send('menu-action', 'start-all-tasks');
                    }
                },
                {
                    label: 'Stop All Tasks',
                    click: () => {
                        mainWindow.webContents.send('menu-action', 'stop-all-tasks');
                    }
                },
                { type: 'separator' },
                {
                    label: 'View Logs',
                    click: () => {
                        mainWindow.webContents.send('menu-action', 'view-logs');
                    }
                }
            ]
        },
        {
            label: 'Help',
            submenu: [
                {
                    label: 'About',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'About TikTok Automation',
                            message: 'TikTok Automation v1.0.0',
                            detail: 'Advanced TikTok automation platform with antidetect browser profiles.'
                        });
                    }
                },
                {
                    label: 'Documentation',
                    click: () => {
                        shell.openExternal('https://github.com/your-repo/tiktok-automation');
                    }
                }
            ]
        }
    ];

    // macOS specific menu adjustments
    if (process.platform === 'darwin') {
        template.unshift({
            label: app.getName(),
            submenu: [
                { role: 'about' },
                { type: 'separator' },
                { role: 'services' },
                { type: 'separator' },
                { role: 'hide' },
                { role: 'hideOthers' },
                { role: 'unhide' },
                { type: 'separator' },
                { role: 'quit' }
            ]
        });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

/**
 * Setup system tray
 */
function setupTray() {
    tray = new Tray(getIconPath());
    
    const contextMenu = Menu.buildFromTemplate([
        {
            label: 'Show TikTok Automation',
            click: () => {
                mainWindow.show();
                mainWindow.focus();
            }
        },
        { type: 'separator' },
        {
            label: 'Backend Status',
            enabled: false
        },
        {
            label: 'Running Tasks: 0',
            enabled: false
        },
        { type: 'separator' },
        {
            label: 'Quit',
            click: () => {
                isQuitting = true;
                app.quit();
            }
        }
    ]);
    
    tray.setToolTip('TikTok Automation');
    tray.setContextMenu(contextMenu);
    
    // Double click to show window
    tray.on('double-click', () => {
        mainWindow.show();
        mainWindow.focus();
    });
}

/**
 * Start Python backend server
 */
async function startBackend() {
    return new Promise((resolve, reject) => {
        console.log('Starting Python backend...');
        
        const pythonPath = isDev ? 'python' : path.join(process.resourcesPath, 'python', 'python');
        const backendScript = isDev 
            ? path.join(__dirname, '../backend/main.py')
            : path.join(process.resourcesPath, 'backend', 'main.py');
        
        backendProcess = spawn(pythonPath, [backendScript], {
            cwd: isDev ? path.join(__dirname, '../backend') : path.join(process.resourcesPath, 'backend'),
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        backendProcess.stdout.on('data', (data) => {
            console.log(`Backend: ${data}`);
        });
        
        backendProcess.stderr.on('data', (data) => {
            console.error(`Backend Error: ${data}`);
        });
        
        backendProcess.on('close', (code) => {
            console.log(`Backend process exited with code ${code}`);
            if (code !== 0 && !isQuitting) {
                // Backend crashed, show error
                dialog.showErrorBox(
                    'Backend Error',
                    'The backend server has stopped unexpectedly. Please restart the application.'
                );
            }
        });
        
        // Wait for backend to start
        setTimeout(() => {
            checkBackendHealth()
                .then(() => {
                    console.log('Backend started successfully');
                    resolve();
                })
                .catch((error) => {
                    console.error('Backend failed to start:', error);
                    reject(error);
                });
        }, 3000);
    });
}

/**
 * Check backend health
 */
async function checkBackendHealth() {
    const fetch = require('node-fetch');
    
    try {
        const response = await fetch(`${BACKEND_URL}/health`);
        if (response.ok) {
            return await response.json();
        } else {
            throw new Error(`Backend health check failed: ${response.status}`);
        }
    } catch (error) {
        throw new Error(`Cannot connect to backend: ${error.message}`);
    }
}

/**
 * Stop backend server
 */
function stopBackend() {
    if (backendProcess) {
        console.log('Stopping backend...');
        backendProcess.kill();
        backendProcess = null;
    }
}

/**
 * Get application icon path
 */
function getIconPath() {
    if (process.platform === 'win32') {
        return path.join(__dirname, 'assets', 'icon.ico');
    } else if (process.platform === 'darwin') {
        return path.join(__dirname, 'assets', 'icon.icns');
    } else {
        return path.join(__dirname, 'assets', 'icon.png');
    }
}

/**
 * App event handlers
 */

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
    // Set app user model ID for Windows
    if (process.platform === 'win32') {
        app.setAppUserModelId('com.tiktokautomation.app');
    }
    
    try {
        // Start backend first
        await startBackend();
        
        // Create main window
        createMainWindow();
        
        // Setup auto updater
        if (!isDev) {
            autoUpdater.checkForUpdatesAndNotify();
        }
        
    } catch (error) {
        console.error('Failed to start application:', error);
        
        dialog.showErrorBox(
            'Startup Error',
            'Failed to start the application backend. Please check your installation.'
        );
        
        app.quit();
    }
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        isQuitting = true;
        app.quit();
    }
});

// Re-create window on macOS when dock icon is clicked
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
    } else {
        mainWindow.show();
    }
});

// App is quitting
app.on('before-quit', () => {
    isQuitting = true;
    stopBackend();
});

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        // Someone tried to run a second instance, focus our window instead
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}

/**
 * IPC Handlers
 */

// Get backend URL
ipcMain.handle('get-backend-url', () => {
    return BACKEND_URL;
});

// Check backend status
ipcMain.handle('check-backend-status', async () => {
    try {
        const health = await checkBackendHealth();
        return { status: 'connected', data: health };
    } catch (error) {
        return { status: 'disconnected', error: error.message };
    }
});

// Show save dialog
ipcMain.handle('show-save-dialog', async (event, options) => {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
});

// Show open dialog
ipcMain.handle('show-open-dialog', async (event, options) => {
    const result = await dialog.showOpenDialog(mainWindow, options);
    return result;
});

// Show message box
ipcMain.handle('show-message-box', async (event, options) => {
    const result = await dialog.showMessageBox(mainWindow, options);
    return result;
});

// Open external URL
ipcMain.handle('open-external', async (event, url) => {
    await shell.openExternal(url);
});

// Get app version
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

// Update tray status
ipcMain.on('update-tray-status', (event, data) => {
    if (tray) {
        const contextMenu = Menu.buildFromTemplate([
            {
                label: 'Show TikTok Automation',
                click: () => {
                    mainWindow.show();
                    mainWindow.focus();
                }
            },
            { type: 'separator' },
            {
                label: `Backend: ${data.backend || 'Unknown'}`,
                enabled: false
            },
            {
                label: `Running Tasks: ${data.runningTasks || 0}`,
                enabled: false
            },
            { type: 'separator' },
            {
                label: 'Quit',
                click: () => {
                    isQuitting = true;
                    app.quit();
                }
            }
        ]);
        
        tray.setContextMenu(contextMenu);
    }
});

// Auto updater events
if (!isDev) {
    autoUpdater.on('update-available', () => {
        dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'Update Available',
            message: 'A new version is available. It will be downloaded in the background.',
            buttons: ['OK']
        });
    });
    
    autoUpdater.on('update-downloaded', () => {
        dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'Update Ready',
            message: 'Update downloaded. The application will restart to apply the update.',
            buttons: ['Restart Now', 'Later']
        }).then((result) => {
            if (result.response === 0) {
                autoUpdater.quitAndInstall();
            }
        });
    });
}
