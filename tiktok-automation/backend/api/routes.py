"""
Main API router
"""

from fastapi import APIRouter

from .endpoints import profiles, proxies, accounts, tasks, system

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(profiles.router, prefix="/profiles", tags=["profiles"])
api_router.include_router(proxies.router, prefix="/proxies", tags=["proxies"])
api_router.include_router(accounts.router, prefix="/accounts", tags=["accounts"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(system.router, prefix="/system", tags=["system"])
