"""
System management API endpoints
"""

from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from core.database import check_db_health
from camoufox_integration.browser_manager import BrowserManager

router = APIRouter()


class SystemStatusResponse(BaseModel):
    status: str
    version: str
    database: str
    memory_usage: Dict[str, Any]
    browser_instances: int


@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status():
    """Get system status and health"""
    try:
        # Check database health
        db_healthy = await check_db_health()
        
        # Get browser manager instance
        browser_manager = BrowserManager()
        memory_usage = await browser_manager.get_memory_usage()
        
        return SystemStatusResponse(
            status="healthy" if db_healthy else "unhealthy",
            version="1.0.0",
            database="connected" if db_healthy else "disconnected",
            memory_usage=memory_usage,
            browser_instances=memory_usage.get("total_browsers", 0)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Simple health check endpoint"""
    return {"status": "ok", "message": "TikTok Automation API is running"}


@router.get("/memory")
async def get_memory_usage():
    """Get detailed memory usage"""
    try:
        browser_manager = BrowserManager()
        memory_usage = await browser_manager.get_memory_usage()
        return memory_usage
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup")
async def cleanup_resources():
    """Cleanup system resources"""
    try:
        browser_manager = BrowserManager()
        await browser_manager.cleanup_all()
        return {"message": "Resources cleaned up successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
