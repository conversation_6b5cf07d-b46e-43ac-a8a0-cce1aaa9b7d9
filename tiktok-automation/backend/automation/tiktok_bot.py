"""
TikTok Bot Core - Main automation engine for TikTok interactions
"""

import asyncio
import random
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from loguru import logger
from playwright.async_api import Page, <PERSON>rowser, BrowserContext

from models.tiktok_account import TikTokAccount
from models.browser_profile import Browser<PERSON>rofile
from models.proxy import Proxy
from services.cookie_service import CookieService
from camoufox_integration.browser_manager import BrowserManager
from .human_behavior import <PERSON>Behavi<PERSON>
from .rate_limiter import RateLimiter


class TikTokBot:
    """Core TikTok automation bot with human-like behavior"""
    
    def __init__(self):
        self.browser_manager = BrowserManager()
        self.cookie_service = CookieService()
        self.human_behavior = HumanBehavior()
        self.rate_limiter = RateLimiter()
        
        # Current session state
        self.current_browser: Optional[Browser] = None
        self.current_context: Optional[BrowserContext] = None
        self.current_page: Optional[Page] = None
        self.current_account: Optional[TikTokAccount] = None
        
        # Bot statistics
        self.session_stats = {
            "follows": 0,
            "unfollows": 0,
            "likes": 0,
            "comments": 0,
            "views": 0,
            "errors": 0,
            "start_time": None
        }
    
    async def initialize_session(
        self,
        account: TikTokAccount,
        profile: BrowserProfile,
        proxy: Optional[Proxy] = None,
        headless: bool = True
    ) -> bool:
        """Initialize bot session with account and browser"""
        
        try:
            logger.info(f"Initializing TikTok bot session for account: {account.username}")
            
            # Store current account
            self.current_account = account
            
            # Launch browser
            self.current_browser = await self.browser_manager.create_browser_instance(
                profile=profile,
                proxy=proxy,
                headless=headless
            )
            
            # Create context
            self.current_context = await self.browser_manager.create_browser_context(
                self.current_browser, profile, proxy
            )
            
            # Load cookies if available
            cookies = await self.cookie_service.load_cookies(account.id)
            if cookies:
                await self.current_context.add_cookies(cookies)
                logger.info(f"Loaded {len(cookies)} cookies for {account.username}")
            
            # Create page
            self.current_page = await self.current_context.new_page()
            
            # Navigate to TikTok
            await self.current_page.goto("https://www.tiktok.com/", timeout=30000)
            
            # Check login status
            is_logged_in = await self._check_login_status()
            
            if not is_logged_in and cookies:
                logger.warning(f"Cookies may be expired for {account.username}")
                # Try refreshing the page
                await self.current_page.reload()
                is_logged_in = await self._check_login_status()
            
            # Initialize session stats
            self.session_stats = {
                "follows": 0,
                "unfollows": 0,
                "likes": 0,
                "comments": 0,
                "views": 0,
                "errors": 0,
                "start_time": datetime.utcnow(),
                "logged_in": is_logged_in
            }
            
            logger.info(f"Bot session initialized. Logged in: {is_logged_in}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize bot session: {e}")
            await self.cleanup_session()
            return False
    
    async def follow_user(self, username: str) -> Dict[str, Any]:
        """Follow a TikTok user with human-like behavior"""
        
        try:
            # Check rate limits
            rate_check = await self.rate_limiter.can_perform_action(
                self.current_account.id, "follow"
            )
            
            if not rate_check["can_proceed"]:
                return {
                    "success": False,
                    "error": "Rate limit exceeded",
                    "wait_time": rate_check["wait_time"]
                }
            
            # Navigate to user profile
            profile_url = f"https://www.tiktok.com/@{username}"
            await self.current_page.goto(profile_url, timeout=30000)
            
            # Wait for page to load
            await self.human_behavior.human_delay(2, 4, "page_load")
            
            # Check if user exists
            if await self._check_user_not_found():
                return {"success": False, "error": "User not found"}
            
            # Check if already following
            if await self._check_already_following():
                return {"success": False, "error": "Already following"}
            
            # Find follow button
            follow_selectors = [
                '[data-e2e="follow-button"]',
                'button[data-e2e="follow-button"]',
                'button:has-text("Follow")',
                'button:has-text("关注")',  # Chinese
                '.follow-button'
            ]
            
            follow_button = None
            for selector in follow_selectors:
                try:
                    follow_button = await self.current_page.wait_for_selector(
                        selector, timeout=5000
                    )
                    if follow_button:
                        break
                except:
                    continue
            
            if not follow_button:
                return {"success": False, "error": "Follow button not found"}
            
            # Simulate reading profile before following
            await self.human_behavior.simulate_reading(200)
            
            # Human-like scroll to follow button
            await self.human_behavior.human_scroll(self.current_page, "up", 100)
            await self.human_behavior.human_delay(0.5, 1.5, "scroll")
            
            # Click follow button with human behavior
            success = await self.human_behavior.human_click(
                self.current_page, follow_selectors[0]
            )
            
            if not success:
                return {"success": False, "error": "Failed to click follow button"}
            
            # Wait for follow action to complete
            await self.human_behavior.human_delay(1, 3, "follow")
            
            # Verify follow was successful
            follow_success = await self._verify_follow_success()
            
            # Record action
            await self.rate_limiter.record_action(
                self.current_account.id, "follow", follow_success
            )
            
            # Update stats
            if follow_success:
                self.session_stats["follows"] += 1
                self.human_behavior.log_action("follow", username, "success")
            else:
                self.session_stats["errors"] += 1
                self.human_behavior.log_action("follow", username, "failed")
            
            return {
                "success": follow_success,
                "username": username,
                "message": "Follow successful" if follow_success else "Follow failed"
            }
            
        except Exception as e:
            logger.error(f"Error following user {username}: {e}")
            self.session_stats["errors"] += 1
            return {"success": False, "error": str(e)}
    
    async def unfollow_user(self, username: str) -> Dict[str, Any]:
        """Unfollow a TikTok user"""
        
        try:
            # Check rate limits
            rate_check = await self.rate_limiter.can_perform_action(
                self.current_account.id, "unfollow"
            )
            
            if not rate_check["can_proceed"]:
                return {
                    "success": False,
                    "error": "Rate limit exceeded",
                    "wait_time": rate_check["wait_time"]
                }
            
            # Navigate to user profile
            profile_url = f"https://www.tiktok.com/@{username}"
            await self.current_page.goto(profile_url, timeout=30000)
            
            await self.human_behavior.human_delay(2, 4, "page_load")
            
            # Check if user exists
            if await self._check_user_not_found():
                return {"success": False, "error": "User not found"}
            
            # Check if not following
            if not await self._check_already_following():
                return {"success": False, "error": "Not following user"}
            
            # Find unfollow button (usually "Following" button)
            unfollow_selectors = [
                '[data-e2e="following-button"]',
                'button[data-e2e="following-button"]',
                'button:has-text("Following")',
                'button:has-text("已关注")',  # Chinese
                '.following-button'
            ]
            
            unfollow_button = None
            for selector in unfollow_selectors:
                try:
                    unfollow_button = await self.current_page.wait_for_selector(
                        selector, timeout=5000
                    )
                    if unfollow_button:
                        break
                except:
                    continue
            
            if not unfollow_button:
                return {"success": False, "error": "Unfollow button not found"}
            
            # Click unfollow button
            success = await self.human_behavior.human_click(
                self.current_page, unfollow_selectors[0]
            )
            
            if not success:
                return {"success": False, "error": "Failed to click unfollow button"}
            
            # Handle confirmation dialog if it appears
            try:
                confirm_button = await self.current_page.wait_for_selector(
                    'button:has-text("Unfollow")', timeout=3000
                )
                if confirm_button:
                    await self.human_behavior.human_click(
                        self.current_page, 'button:has-text("Unfollow")'
                    )
            except:
                pass  # No confirmation dialog
            
            await self.human_behavior.human_delay(1, 3, "unfollow")
            
            # Verify unfollow was successful
            unfollow_success = not await self._check_already_following()
            
            # Record action
            await self.rate_limiter.record_action(
                self.current_account.id, "unfollow", unfollow_success
            )
            
            # Update stats
            if unfollow_success:
                self.session_stats["unfollows"] += 1
                self.human_behavior.log_action("unfollow", username, "success")
            else:
                self.session_stats["errors"] += 1
                self.human_behavior.log_action("unfollow", username, "failed")
            
            return {
                "success": unfollow_success,
                "username": username,
                "message": "Unfollow successful" if unfollow_success else "Unfollow failed"
            }
            
        except Exception as e:
            logger.error(f"Error unfollowing user {username}: {e}")
            self.session_stats["errors"] += 1
            return {"success": False, "error": str(e)}
    
    async def like_video(self, video_url: str) -> Dict[str, Any]:
        """Like a TikTok video"""
        
        try:
            # Check rate limits
            rate_check = await self.rate_limiter.can_perform_action(
                self.current_account.id, "like"
            )
            
            if not rate_check["can_proceed"]:
                return {
                    "success": False,
                    "error": "Rate limit exceeded",
                    "wait_time": rate_check["wait_time"]
                }
            
            # Navigate to video
            await self.current_page.goto(video_url, timeout=30000)
            await self.human_behavior.human_delay(2, 4, "page_load")
            
            # Watch video for a bit (human behavior)
            watch_time = random.uniform(3, 15)
            await asyncio.sleep(watch_time)
            
            # Find like button
            like_selectors = [
                '[data-e2e="like-button"]',
                'button[data-e2e="like-button"]',
                '.like-button',
                '[aria-label*="like"]'
            ]
            
            like_button = None
            for selector in like_selectors:
                try:
                    like_button = await self.current_page.wait_for_selector(
                        selector, timeout=5000
                    )
                    if like_button:
                        break
                except:
                    continue
            
            if not like_button:
                return {"success": False, "error": "Like button not found"}
            
            # Check if already liked
            is_liked = await self._check_already_liked()
            if is_liked:
                return {"success": False, "error": "Already liked"}
            
            # Click like button
            success = await self.human_behavior.human_click(
                self.current_page, like_selectors[0]
            )
            
            if not success:
                return {"success": False, "error": "Failed to click like button"}
            
            await self.human_behavior.human_delay(0.5, 2, "like")
            
            # Verify like was successful
            like_success = await self._check_already_liked()
            
            # Record action
            await self.rate_limiter.record_action(
                self.current_account.id, "like", like_success
            )
            
            # Update stats
            if like_success:
                self.session_stats["likes"] += 1
                self.human_behavior.log_action("like", video_url, "success")
            else:
                self.session_stats["errors"] += 1
                self.human_behavior.log_action("like", video_url, "failed")
            
            return {
                "success": like_success,
                "video_url": video_url,
                "watch_time": watch_time,
                "message": "Like successful" if like_success else "Like failed"
            }
            
        except Exception as e:
            logger.error(f"Error liking video {video_url}: {e}")
            self.session_stats["errors"] += 1
            return {"success": False, "error": str(e)}
    
    async def get_user_followers(
        self,
        username: str,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get list of user's followers"""
        
        try:
            # Navigate to user profile
            profile_url = f"https://www.tiktok.com/@{username}"
            await self.current_page.goto(profile_url, timeout=30000)
            
            await self.human_behavior.human_delay(2, 4, "page_load")
            
            # Click on followers count
            followers_selectors = [
                '[data-e2e="followers-count"]',
                'a[href*="/followers"]',
                'strong:has-text("Followers")'
            ]
            
            followers_link = None
            for selector in followers_selectors:
                try:
                    followers_link = await self.current_page.wait_for_selector(
                        selector, timeout=5000
                    )
                    if followers_link:
                        break
                except:
                    continue
            
            if not followers_link:
                return []
            
            # Click to open followers list
            await self.human_behavior.human_click(self.current_page, followers_selectors[0])
            await self.human_behavior.human_delay(2, 4, "page_load")
            
            # Scrape followers
            followers = []
            scraped_count = 0
            
            while scraped_count < limit:
                # Get current followers on page
                current_followers = await self._extract_followers_from_page()
                
                # Add new followers
                for follower in current_followers:
                    if follower not in followers and scraped_count < limit:
                        followers.append(follower)
                        scraped_count += 1
                
                # Scroll to load more
                if scraped_count < limit:
                    await self.human_behavior.human_scroll(
                        self.current_page, "down", 500
                    )
                    await self.human_behavior.human_delay(1, 3, "scroll")
                else:
                    break
            
            logger.info(f"Scraped {len(followers)} followers for {username}")
            return followers
            
        except Exception as e:
            logger.error(f"Error getting followers for {username}: {e}")
            return []
    
    async def cleanup_session(self):
        """Cleanup bot session and resources"""
        
        try:
            # Save cookies if logged in
            if self.current_account and self.session_stats.get("logged_in"):
                if self.current_context:
                    cookies = await self.current_context.cookies()
                    await self.cookie_service.save_cookies(
                        self.current_account.id, cookies
                    )
            
            # Close page
            if self.current_page:
                await self.current_page.close()
                self.current_page = None
            
            # Close context
            if self.current_context:
                await self.browser_manager.close_context(self.current_context)
                self.current_context = None
            
            # Close browser
            if self.current_browser:
                await self.browser_manager.close_browser(self.current_browser)
                self.current_browser = None
            
            # Log session stats
            if self.session_stats.get("start_time"):
                duration = datetime.utcnow() - self.session_stats["start_time"]
                logger.info(f"Bot session ended. Duration: {duration}, Stats: {self.session_stats}")
            
            self.current_account = None
            
        except Exception as e:
            logger.error(f"Error during session cleanup: {e}")
    
    async def _check_login_status(self) -> bool:
        """Check if currently logged in to TikTok"""
        
        try:
            # Look for login indicators
            login_indicators = [
                '[data-e2e="profile-icon"]',
                '[data-e2e="nav-profile"]',
                '.avatar',
                '[href*="/profile"]'
            ]
            
            for selector in login_indicators:
                try:
                    element = await self.current_page.wait_for_selector(
                        selector, timeout=3000
                    )
                    if element:
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking login status: {e}")
            return False
    
    async def _check_user_not_found(self) -> bool:
        """Check if user profile was not found"""
        
        try:
            not_found_indicators = [
                'text="User not found"',
                'text="This account cannot be found"',
                '.not-found',
                '[data-e2e="user-not-found"]'
            ]
            
            for selector in not_found_indicators:
                try:
                    element = await self.current_page.wait_for_selector(
                        selector, timeout=2000
                    )
                    if element:
                        return True
                except:
                    continue
            
            return False
            
        except Exception:
            return False
    
    async def _check_already_following(self) -> bool:
        """Check if already following the user"""
        
        try:
            following_indicators = [
                'button:has-text("Following")',
                'button:has-text("已关注")',
                '[data-e2e="following-button"]'
            ]
            
            for selector in following_indicators:
                try:
                    element = await self.current_page.wait_for_selector(
                        selector, timeout=2000
                    )
                    if element:
                        return True
                except:
                    continue
            
            return False
            
        except Exception:
            return False
    
    async def _check_already_liked(self) -> bool:
        """Check if video is already liked"""
        
        try:
            # Look for liked state indicators
            liked_indicators = [
                '[data-e2e="like-button"][class*="liked"]',
                '[data-e2e="like-button"][aria-pressed="true"]',
                '.like-button.liked'
            ]
            
            for selector in liked_indicators:
                try:
                    element = await self.current_page.wait_for_selector(
                        selector, timeout=2000
                    )
                    if element:
                        return True
                except:
                    continue
            
            return False
            
        except Exception:
            return False
    
    async def _verify_follow_success(self) -> bool:
        """Verify that follow action was successful"""
        
        await asyncio.sleep(1)  # Wait for UI update
        return await self._check_already_following()
    
    async def _extract_followers_from_page(self) -> List[Dict[str, Any]]:
        """Extract follower information from current page"""
        
        try:
            followers = await self.current_page.evaluate("""
                () => {
                    const followers = [];
                    const userElements = document.querySelectorAll('[data-e2e="user-item"]');
                    
                    userElements.forEach(element => {
                        const usernameEl = element.querySelector('[data-e2e="user-username"]');
                        const displayNameEl = element.querySelector('[data-e2e="user-title"]');
                        const avatarEl = element.querySelector('img');
                        
                        if (usernameEl) {
                            followers.push({
                                username: usernameEl.textContent.trim(),
                                display_name: displayNameEl ? displayNameEl.textContent.trim() : '',
                                avatar_url: avatarEl ? avatarEl.src : ''
                            });
                        }
                    });
                    
                    return followers;
                }
            """)
            
            return followers or []
            
        except Exception as e:
            logger.error(f"Error extracting followers: {e}")
            return []
