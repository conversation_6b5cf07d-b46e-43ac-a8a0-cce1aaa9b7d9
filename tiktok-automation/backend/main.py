"""
TikTok Automation Backend Server
Main FastAPI application with WebSocket support
"""

import asyncio
import os
import sys
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from loguru import logger
import uvicorn

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.config import settings
from core.database import init_db
from core.websocket_manager import WebSocketManager
from api.routes import api_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting TikTok Automation Backend...")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    # Initialize WebSocket manager
    app.state.websocket_manager = WebSocketManager()
    logger.info("WebSocket manager initialized")
    
    # Start background tasks
    # asyncio.create_task(start_background_tasks())
    
    yield
    
    # Shutdown
    logger.info("Shutting down TikTok Automation Backend...")


# Create FastAPI app with optimized settings
app = FastAPI(
    title="TikTok Automation API",
    description="Backend API for TikTok Automation Desktop App",
    version="1.0.0",
    lifespan=lifespan,
    # Performance optimizations
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# CORS middleware for Electron frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "file://"],  # Electron origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# WebSocket endpoint for real-time communication
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication with frontend"""
    await app.state.websocket_manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            await app.state.websocket_manager.handle_message(websocket, data)
    except WebSocketDisconnect:
        app.state.websocket_manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        app.state.websocket_manager.disconnect(websocket)


@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "TikTok Automation Backend is running",
        "version": "1.0.0",
        "status": "healthy"
    }


@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "database": "connected",
        "websocket": "active",
        "memory_usage": get_memory_usage(),
    }


def get_memory_usage():
    """Get current memory usage"""
    import psutil
    process = psutil.Process()
    memory_info = process.memory_info()
    return {
        "rss": memory_info.rss / 1024 / 1024,  # MB
        "vms": memory_info.vms / 1024 / 1024,  # MB
        "percent": process.memory_percent()
    }


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO" if not settings.DEBUG else "DEBUG"
    )
    
    # Run the server
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
        # Performance optimizations
        workers=1,  # Single worker for desktop app
        loop="asyncio",
        http="httptools",
        ws="websockets",
        # Memory optimizations
        limit_concurrency=100,
        limit_max_requests=1000,
    )
