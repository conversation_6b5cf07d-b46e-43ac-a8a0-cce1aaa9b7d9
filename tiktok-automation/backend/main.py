"""
TikTok Automation Backend Server
Main FastAPI application with WebSocket support
"""

import asyncio
import os
import sys
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.staticfiles import StaticFiles
from loguru import logger
import uvicorn

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.config import settings
from core.database import init_database, close_database, check_db_health
from core.websocket_manager import WebSocketManager
from api.routes import api_router
from api.middleware import RequestLoggingMiddleware, SecurityHeadersMiddleware
from api.websocket_handlers import WebSocketEventHandler


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting TikTok Automation Backend...")
    
    # Initialize database
    await init_database()
    logger.info("Database initialized")

    # Initialize WebSocket manager
    app.state.websocket_manager = WebSocketManager()
    app.state.websocket_handler = WebSocketEventHandler(app.state.websocket_manager)
    logger.info("WebSocket manager and handlers initialized")

    # Start background tasks
    # asyncio.create_task(start_background_tasks())

    yield

    # Shutdown
    logger.info("Shutting down TikTok Automation Backend...")

    # Close database connections
    await close_database()
    logger.info("Database connections closed")

    # Cleanup WebSocket connections
    for websocket in app.state.websocket_manager.active_connections:
        try:
            await websocket.close()
        except:
            pass

    logger.info("Server shutdown complete")


# Create FastAPI app with optimized settings
app = FastAPI(
    title="TikTok Automation API",
    description="Backend API for TikTok Automation Desktop App",
    version="1.0.0",
    lifespan=lifespan,
    # Performance optimizations
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# Add middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RequestLoggingMiddleware)

# CORS middleware for Electron frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,  # Use settings for origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# WebSocket endpoint for real-time communication
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication with frontend"""
    await app.state.websocket_manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            await app.state.websocket_handler.handle_message(websocket, data)
    except WebSocketDisconnect:
        app.state.websocket_manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        app.state.websocket_manager.disconnect(websocket)


@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "TikTok Automation Backend is running",
        "version": "1.0.0",
        "status": "healthy"
    }


@app.get("/health")
async def health_check():
    """Detailed health check"""

    # Check database health
    db_healthy = await check_db_health()

    # Check WebSocket manager
    ws_stats = app.state.websocket_manager.get_connection_stats()

    return {
        "status": "healthy" if db_healthy else "unhealthy",
        "database": "connected" if db_healthy else "disconnected",
        "websocket": {
            "status": "active",
            "connections": ws_stats["total_connections"],
            "subscriptions": ws_stats["subscriptions"]
        },
        "memory_usage": get_memory_usage(),
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }


def get_memory_usage():
    """Get current memory usage"""
    import psutil
    process = psutil.Process()
    memory_info = process.memory_info()
    return {
        "rss": memory_info.rss / 1024 / 1024,  # MB
        "vms": memory_info.vms / 1024 / 1024,  # MB
        "percent": process.memory_percent()
    }


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO" if not settings.DEBUG else "DEBUG"
    )
    
    # Run the server
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
        # Performance optimizations
        workers=1,  # Single worker for desktop app
        loop="asyncio",
        http="httptools",
        ws="websockets",
        # Memory optimizations
        limit_concurrency=100,
        limit_max_requests=1000,
    )
