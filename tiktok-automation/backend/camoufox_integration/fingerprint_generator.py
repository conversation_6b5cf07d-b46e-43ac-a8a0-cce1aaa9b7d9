"""
Fingerprint Generator for creating realistic browser fingerprints
"""

import random
import json
from typing import Dict, Any, Optional, List
from loguru import logger

try:
    from browserforge import BrowserForge
    BROWSERFORGE_AVAILABLE = True
except ImportError:
    BROWSERFORGE_AVAILABLE = False
    logger.warning("BrowserForge not available, using fallback fingerprint generation")


class FingerprintGenerator:
    """Generates realistic browser fingerprints for antidetect purposes"""
    
    def __init__(self):
        self.browserforge = None
        if BROWSERFORGE_AVAILABLE:
            try:
                self.browserforge = BrowserForge()
            except Exception as e:
                logger.warning(f"Failed to initialize BrowserForge: {e}")
        
        # Fallback fingerprint data
        self.fallback_data = self._load_fallback_data()
    
    async def generate_fingerprint(
        self,
        os_preference: str = "windows",
        browser_preference: str = "firefox",
        mobile: bool = False
    ) -> Dict[str, Any]:
        """Generate a complete browser fingerprint"""
        
        try:
            if self.browserforge:
                return await self._generate_with_browserforge(
                    os_preference, browser_preference, mobile
                )
            else:
                return await self._generate_fallback_fingerprint(
                    os_preference, browser_preference, mobile
                )
        except Exception as e:
            logger.error(f"Error generating fingerprint: {e}")
            return await self._generate_fallback_fingerprint(
                os_preference, browser_preference, mobile
            )
    
    async def _generate_with_browserforge(
        self,
        os_preference: str,
        browser_preference: str,
        mobile: bool
    ) -> Dict[str, Any]:
        """Generate fingerprint using BrowserForge"""
        
        try:
            # Generate fingerprint with BrowserForge
            fingerprint = self.browserforge.generate(
                browser=browser_preference,
                os=os_preference,
                mobile=mobile
            )
            
            # Convert to Camoufox format
            camoufox_config = self._convert_to_camoufox_format(fingerprint)
            
            logger.debug(f"Generated fingerprint with BrowserForge for {os_preference}/{browser_preference}")
            return camoufox_config
            
        except Exception as e:
            logger.error(f"BrowserForge generation failed: {e}")
            raise
    
    async def _generate_fallback_fingerprint(
        self,
        os_preference: str,
        browser_preference: str,
        mobile: bool
    ) -> Dict[str, Any]:
        """Generate fingerprint using fallback data"""
        
        logger.info(f"Generating fallback fingerprint for {os_preference}/{browser_preference}")
        
        # Get base configuration for OS
        base_config = self.fallback_data.get(os_preference, self.fallback_data["windows"])
        
        # Generate navigator properties
        navigator_config = self._generate_navigator_config(base_config, mobile)
        
        # Generate screen configuration
        screen_config = self._generate_screen_config(mobile)
        
        # Generate window configuration
        window_config = self._generate_window_config(screen_config)
        
        # Generate WebGL configuration
        webgl_config = self._generate_webgl_config(os_preference)
        
        # Generate audio configuration
        audio_config = self._generate_audio_config()
        
        # Generate geolocation (random)
        geolocation_config = self._generate_geolocation_config()
        
        # Combine all configurations
        fingerprint = {
            **navigator_config,
            **screen_config,
            **window_config,
            **webgl_config,
            **audio_config,
            **geolocation_config,
        }
        
        return fingerprint
    
    def _generate_navigator_config(self, base_config: Dict[str, Any], mobile: bool) -> Dict[str, Any]:
        """Generate navigator properties"""
        
        config = {}
        
        # User agent
        user_agents = base_config.get("user_agents", [])
        if user_agents:
            config["navigator.userAgent"] = random.choice(user_agents)
        
        # Platform
        platforms = base_config.get("platforms", ["Win32"])
        config["navigator.platform"] = random.choice(platforms)
        
        # Hardware concurrency
        config["navigator.hardwareConcurrency"] = random.choice([2, 4, 6, 8, 12, 16])
        
        # Memory (if not mobile)
        if not mobile:
            config["navigator.deviceMemory"] = random.choice([2, 4, 8, 16])
        
        # Language
        config["navigator.language"] = "en-US"
        config["navigator.languages"] = ["en-US", "en"]
        
        # Other properties
        config["navigator.cookieEnabled"] = True
        config["navigator.doNotTrack"] = random.choice([None, "1"])
        config["navigator.maxTouchPoints"] = 0 if not mobile else random.choice([5, 10])
        
        return config
    
    def _generate_screen_config(self, mobile: bool) -> Dict[str, Any]:
        """Generate screen properties"""
        
        if mobile:
            # Mobile screen sizes
            screens = [
                {"width": 375, "height": 667},  # iPhone 6/7/8
                {"width": 414, "height": 736},  # iPhone 6/7/8 Plus
                {"width": 375, "height": 812},  # iPhone X/XS
                {"width": 360, "height": 640},  # Android
                {"width": 412, "height": 732},  # Android
            ]
        else:
            # Desktop screen sizes
            screens = [
                {"width": 1920, "height": 1080},
                {"width": 1366, "height": 768},
                {"width": 1440, "height": 900},
                {"width": 1536, "height": 864},
                {"width": 1600, "height": 900},
                {"width": 2560, "height": 1440},
            ]
        
        screen = random.choice(screens)
        
        return {
            "screen.width": screen["width"],
            "screen.height": screen["height"],
            "screen.availWidth": screen["width"],
            "screen.availHeight": screen["height"] - random.choice([0, 30, 40]),
            "screen.colorDepth": 24,
            "screen.pixelDepth": 24,
        }
    
    def _generate_window_config(self, screen_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate window properties"""
        
        screen_width = screen_config["screen.width"]
        screen_height = screen_config["screen.height"]
        
        # Window size should be smaller than screen
        window_width = screen_width - random.randint(0, 100)
        window_height = screen_height - random.randint(50, 150)
        
        # Inner size (viewport)
        inner_width = window_width - random.randint(0, 20)
        inner_height = window_height - random.randint(80, 120)
        
        return {
            "window.outerWidth": window_width,
            "window.outerHeight": window_height,
            "window.innerWidth": inner_width,
            "window.innerHeight": inner_height,
            "window.screenX": random.randint(0, 100),
            "window.screenY": random.randint(0, 100),
            "window.devicePixelRatio": random.choice([1, 1.25, 1.5, 2]),
        }
    
    def _generate_webgl_config(self, os_preference: str) -> Dict[str, Any]:
        """Generate WebGL configuration"""
        
        # WebGL renderers by OS
        renderers = {
            "windows": [
                "ANGLE (NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0)",
            ],
            "macos": [
                "Intel Iris Pro OpenGL Engine",
                "AMD Radeon Pro 560 OpenGL Engine",
                "Apple M1 OpenGL Engine",
            ],
            "linux": [
                "Mesa DRI Intel(R) HD Graphics 620",
                "NVIDIA GeForce GTX 1060/PCIe/SSE2",
                "AMD Radeon RX 580",
            ]
        }
        
        vendors = {
            "windows": "Google Inc. (ANGLE)",
            "macos": "Intel Inc.",
            "linux": "Mesa/X.org"
        }
        
        renderer_list = renderers.get(os_preference, renderers["windows"])
        vendor = vendors.get(os_preference, vendors["windows"])
        
        return {
            "webGl:vendor": vendor,
            "webGl:renderer": random.choice(renderer_list),
        }
    
    def _generate_audio_config(self) -> Dict[str, Any]:
        """Generate audio context configuration"""
        
        return {
            "AudioContext:sampleRate": random.choice([44100, 48000]),
            "AudioContext:maxChannelCount": random.choice([2, 6, 8]),
            "AudioContext:outputLatency": round(random.uniform(0.01, 0.05), 4),
        }
    
    def _generate_geolocation_config(self) -> Dict[str, Any]:
        """Generate random geolocation"""
        
        # Random coordinates (avoid exact locations)
        latitude = round(random.uniform(-85, 85), 6)
        longitude = round(random.uniform(-180, 180), 6)
        
        return {
            "geolocation:latitude": latitude,
            "geolocation:longitude": longitude,
            "geolocation:accuracy": random.randint(10, 100),
        }
    
    def _convert_to_camoufox_format(self, browserforge_fingerprint: Dict[str, Any]) -> Dict[str, Any]:
        """Convert BrowserForge fingerprint to Camoufox format"""
        
        config = {}
        
        # Map BrowserForge fields to Camoufox format
        if "userAgent" in browserforge_fingerprint:
            config["navigator.userAgent"] = browserforge_fingerprint["userAgent"]
        
        if "platform" in browserforge_fingerprint:
            config["navigator.platform"] = browserforge_fingerprint["platform"]
        
        if "screen" in browserforge_fingerprint:
            screen = browserforge_fingerprint["screen"]
            for key, value in screen.items():
                config[f"screen.{key}"] = value
        
        if "viewport" in browserforge_fingerprint:
            viewport = browserforge_fingerprint["viewport"]
            config["window.innerWidth"] = viewport.get("width")
            config["window.innerHeight"] = viewport.get("height")
        
        # Add other mappings as needed
        
        return config
    
    def _load_fallback_data(self) -> Dict[str, Any]:
        """Load fallback fingerprint data"""
        
        return {
            "windows": {
                "user_agents": [
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/117.0",
                ],
                "platforms": ["Win32"],
            },
            "macos": {
                "user_agents": [
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/118.0",
                ],
                "platforms": ["MacIntel"],
            },
            "linux": {
                "user_agents": [
                    "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/119.0",
                    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/119.0",
                ],
                "platforms": ["Linux x86_64"],
            }
        }
