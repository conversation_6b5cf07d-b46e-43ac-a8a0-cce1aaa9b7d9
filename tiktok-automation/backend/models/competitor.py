"""
Competitor model for tracking competitor profiles and followers
"""

from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid_property import hybrid_property

from core.database import Base


class Competitor(Base):
    """Competitor profile for follower analysis and automation"""
    
    __tablename__ = "competitors"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic info
    username = Column(String(255), nullable=False, unique=True, index=True)
    display_name = Column(String(255), nullable=True)
    tiktok_url = Column(Text, nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_monitoring = Column(Boolean, default=False, nullable=False)
    
    # Profile information
    profile_picture_url = Column(Text, nullable=True)
    bio = Column(Text, nullable=True)
    follower_count = Column(Integer, default=0, nullable=False)
    following_count = Column(Integer, default=0, nullable=False)
    likes_count = Column(Integer, default=0, nullable=False)
    videos_count = Column(Integer, default=0, nullable=False)
    
    # Verification status
    is_verified = Column(Boolean, default=False, nullable=False)
    is_private = Column(Boolean, default=False, nullable=False)
    
    # Analysis data
    category = Column(String(100), nullable=True)  # e.g., "fashion", "tech", "entertainment"
    tags = Column(JSON, nullable=True, default=list)  # List of tags
    
    # Follower tracking
    total_followers_scraped = Column(Integer, default=0, nullable=False)
    last_follower_scrape = Column(DateTime, nullable=True)
    follower_scrape_count = Column(Integer, default=0, nullable=False)
    
    # Following tracking  
    total_following_scraped = Column(Integer, default=0, nullable=False)
    last_following_scrape = Column(DateTime, nullable=True)
    following_scrape_count = Column(Integer, default=0, nullable=False)
    
    # Automation settings
    auto_follow_followers = Column(Boolean, default=False, nullable=False)
    auto_follow_following = Column(Boolean, default=False, nullable=False)
    follow_limit_per_day = Column(Integer, default=50, nullable=False)
    follow_delay_min = Column(Integer, default=2, nullable=False)  # seconds
    follow_delay_max = Column(Integer, default=5, nullable=False)  # seconds
    
    # Filtering criteria
    filter_criteria = Column(JSON, nullable=True, default=dict)  # Follower filtering rules
    
    # Performance tracking
    total_follows_attempted = Column(Integer, default=0, nullable=False)
    total_follows_successful = Column(Integer, default=0, nullable=False)
    total_follows_failed = Column(Integer, default=0, nullable=False)
    
    # Health and errors
    error_count = Column(Integer, default=0, nullable=False)
    last_error = Column(Text, nullable=True)
    last_error_time = Column(DateTime, nullable=True)
    
    # Notes and description
    notes = Column(Text, nullable=True)
    priority = Column(Integer, default=1, nullable=False)  # 1=low, 5=high
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_checked = Column(DateTime, nullable=True)
    
    # Relationships
    follow_tasks = relationship("FollowTask", back_populates="competitor")
    
    @hybrid_property
    def follow_success_rate(self) -> float:
        """Calculate follow success rate percentage"""
        if self.total_follows_attempted == 0:
            return 0.0
        return (self.total_follows_successful / self.total_follows_attempted) * 100
    
    @hybrid_property
    def engagement_rate(self) -> float:
        """Calculate engagement rate"""
        if self.follower_count == 0:
            return 0.0
        return (self.likes_count / self.follower_count) * 100
    
    @hybrid_property
    def scraping_stats(self) -> Dict[str, Any]:
        """Get scraping statistics"""
        return {
            "followers_scraped": self.total_followers_scraped,
            "following_scraped": self.total_following_scraped,
            "last_follower_scrape": self.last_follower_scrape.isoformat() if self.last_follower_scrape else None,
            "last_following_scrape": self.last_following_scrape.isoformat() if self.last_following_scrape else None,
            "follower_scrape_count": self.follower_scrape_count,
            "following_scrape_count": self.following_scrape_count
        }
    
    @hybrid_property
    def automation_stats(self) -> Dict[str, Any]:
        """Get automation statistics"""
        return {
            "total_attempts": self.total_follows_attempted,
            "successful": self.total_follows_successful,
            "failed": self.total_follows_failed,
            "success_rate": self.follow_success_rate,
            "error_count": self.error_count
        }
    
    def update_profile_stats(self, stats: Dict[str, Any]):
        """Update competitor profile statistics"""
        self.follower_count = stats.get("followers", self.follower_count)
        self.following_count = stats.get("following", self.following_count)
        self.likes_count = stats.get("likes", self.likes_count)
        self.videos_count = stats.get("videos", self.videos_count)
        self.is_verified = stats.get("is_verified", self.is_verified)
        self.is_private = stats.get("is_private", self.is_private)
        self.last_checked = datetime.utcnow()
    
    def update_scraping_stats(self, scrape_type: str, count: int):
        """Update scraping statistics"""
        if scrape_type == "followers":
            self.total_followers_scraped += count
            self.last_follower_scrape = datetime.utcnow()
            self.follower_scrape_count += 1
        elif scrape_type == "following":
            self.total_following_scraped += count
            self.last_following_scrape = datetime.utcnow()
            self.following_scrape_count += 1
    
    def update_follow_stats(self, success: bool):
        """Update follow attempt statistics"""
        self.total_follows_attempted += 1
        if success:
            self.total_follows_successful += 1
        else:
            self.total_follows_failed += 1
    
    def add_error(self, error_message: str):
        """Add error to competitor"""
        self.error_count += 1
        self.last_error = error_message
        self.last_error_time = datetime.utcnow()
    
    def get_filter_criteria(self) -> Dict[str, Any]:
        """Get follower filtering criteria"""
        default_criteria = {
            "min_followers": 0,
            "max_followers": None,
            "min_following": 0,
            "max_following": None,
            "verified_only": False,
            "exclude_private": True,
            "exclude_business": False,
            "min_videos": 0,
            "keywords_include": [],
            "keywords_exclude": [],
            "languages": []
        }
        
        if self.filter_criteria:
            default_criteria.update(self.filter_criteria)
        
        return default_criteria
    
    def set_filter_criteria(self, criteria: Dict[str, Any]):
        """Set follower filtering criteria"""
        self.filter_criteria = criteria
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "id": self.id,
            "username": self.username,
            "display_name": self.display_name,
            "tiktok_url": self.tiktok_url,
            "is_active": self.is_active,
            "is_monitoring": self.is_monitoring,
            "profile_picture_url": self.profile_picture_url,
            "bio": self.bio,
            "follower_count": self.follower_count,
            "following_count": self.following_count,
            "likes_count": self.likes_count,
            "videos_count": self.videos_count,
            "is_verified": self.is_verified,
            "is_private": self.is_private,
            "category": self.category,
            "tags": self.tags or [],
            "auto_follow_followers": self.auto_follow_followers,
            "auto_follow_following": self.auto_follow_following,
            "follow_limit_per_day": self.follow_limit_per_day,
            "follow_delay_min": self.follow_delay_min,
            "follow_delay_max": self.follow_delay_max,
            "filter_criteria": self.get_filter_criteria(),
            "scraping_stats": self.scraping_stats,
            "automation_stats": self.automation_stats,
            "engagement_rate": self.engagement_rate,
            "error_count": self.error_count,
            "last_error": self.last_error,
            "last_error_time": self.last_error_time.isoformat() if self.last_error_time else None,
            "notes": self.notes,
            "priority": self.priority,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_checked": self.last_checked.isoformat() if self.last_checked else None,
        }
    
    def __repr__(self):
        return f"<Competitor(id={self.id}, username='{self.username}', active={self.is_active})>"
