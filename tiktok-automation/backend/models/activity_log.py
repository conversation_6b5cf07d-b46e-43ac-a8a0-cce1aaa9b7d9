"""
Activity Log model for tracking all system activities
"""

from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum

from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid_property import hybrid_property

from core.database import Base


class LogLevel(str, Enum):
    """Log level enumeration"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ActivityType(str, Enum):
    """Activity type enumeration"""
    # Authentication
    LOGIN = "login"
    LOGOUT = "logout"
    LOGIN_FAILED = "login_failed"
    
    # Profile management
    PROFILE_CREATED = "profile_created"
    PROFILE_UPDATED = "profile_updated"
    PROFILE_DELETED = "profile_deleted"
    
    # Account management
    ACCOUNT_ADDED = "account_added"
    ACCOUNT_UPDATED = "account_updated"
    ACCOUNT_DELETED = "account_deleted"
    ACCOUNT_LOGIN = "account_login"
    ACCOUNT_LOGIN_FAILED = "account_login_failed"
    
    # Automation activities
    FOLLOW_ACTION = "follow_action"
    UNFOLLOW_ACTION = "unfollow_action"
    LIKE_ACTION = "like_action"
    COMMENT_ACTION = "comment_action"
    
    # Task management
    TASK_CREATED = "task_created"
    TASK_STARTED = "task_started"
    TASK_PAUSED = "task_paused"
    TASK_RESUMED = "task_resumed"
    TASK_COMPLETED = "task_completed"
    TASK_FAILED = "task_failed"
    TASK_CANCELLED = "task_cancelled"
    
    # Competitor management
    COMPETITOR_ADDED = "competitor_added"
    COMPETITOR_UPDATED = "competitor_updated"
    COMPETITOR_DELETED = "competitor_deleted"
    COMPETITOR_SCRAPED = "competitor_scraped"
    
    # System events
    SYSTEM_START = "system_start"
    SYSTEM_STOP = "system_stop"
    SYSTEM_ERROR = "system_error"
    
    # Browser events
    BROWSER_LAUNCHED = "browser_launched"
    BROWSER_CLOSED = "browser_closed"
    BROWSER_ERROR = "browser_error"
    
    # Proxy events
    PROXY_CONNECTED = "proxy_connected"
    PROXY_DISCONNECTED = "proxy_disconnected"
    PROXY_ERROR = "proxy_error"


class ActivityLog(Base):
    """Activity log for tracking all system activities"""
    
    __tablename__ = "activity_logs"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Log info
    level = Column(SQLEnum(LogLevel), default=LogLevel.INFO, nullable=False, index=True)
    activity_type = Column(SQLEnum(ActivityType), nullable=False, index=True)
    message = Column(Text, nullable=False)
    
    # Context information
    tiktok_account_id = Column(Integer, ForeignKey("tiktok_accounts.id"), nullable=True, index=True)
    browser_profile_id = Column(Integer, ForeignKey("browser_profiles.id"), nullable=True, index=True)
    follow_task_id = Column(Integer, ForeignKey("follow_tasks.id"), nullable=True, index=True)
    competitor_id = Column(Integer, ForeignKey("competitors.id"), nullable=True, index=True)
    
    # Additional data
    metadata = Column(JSON, nullable=True, default=dict)  # Additional context data
    
    # Error information
    error_code = Column(String(50), nullable=True)
    error_details = Column(Text, nullable=True)
    stack_trace = Column(Text, nullable=True)
    
    # Request information
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Performance metrics
    duration_ms = Column(Integer, nullable=True)  # Duration in milliseconds
    memory_usage_mb = Column(Integer, nullable=True)  # Memory usage in MB
    
    # Timestamp
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    # Relationships
    tiktok_account = relationship("TikTokAccount", back_populates="activity_logs")
    browser_profile = relationship("BrowserProfile", backref="activity_logs")
    follow_task = relationship("FollowTask", backref="activity_logs")
    competitor = relationship("Competitor", backref="activity_logs")
    
    @hybrid_property
    def is_error(self) -> bool:
        """Check if this is an error log"""
        return self.level in [LogLevel.ERROR, LogLevel.CRITICAL]
    
    @hybrid_property
    def context_summary(self) -> Dict[str, Any]:
        """Get context summary"""
        context = {}
        
        if self.tiktok_account_id:
            context["account_id"] = self.tiktok_account_id
        
        if self.browser_profile_id:
            context["profile_id"] = self.browser_profile_id
        
        if self.follow_task_id:
            context["task_id"] = self.follow_task_id
        
        if self.competitor_id:
            context["competitor_id"] = self.competitor_id
        
        return context
    
    @classmethod
    def create_log(
        cls,
        level: LogLevel,
        activity_type: ActivityType,
        message: str,
        tiktok_account_id: Optional[int] = None,
        browser_profile_id: Optional[int] = None,
        follow_task_id: Optional[int] = None,
        competitor_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        error_details: Optional[str] = None,
        stack_trace: Optional[str] = None,
        duration_ms: Optional[int] = None,
        memory_usage_mb: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> "ActivityLog":
        """Create a new activity log entry"""
        return cls(
            level=level,
            activity_type=activity_type,
            message=message,
            tiktok_account_id=tiktok_account_id,
            browser_profile_id=browser_profile_id,
            follow_task_id=follow_task_id,
            competitor_id=competitor_id,
            metadata=metadata or {},
            error_code=error_code,
            error_details=error_details,
            stack_trace=stack_trace,
            duration_ms=duration_ms,
            memory_usage_mb=memory_usage_mb,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    @classmethod
    def log_info(cls, activity_type: ActivityType, message: str, **kwargs) -> "ActivityLog":
        """Create info log"""
        return cls.create_log(LogLevel.INFO, activity_type, message, **kwargs)
    
    @classmethod
    def log_warning(cls, activity_type: ActivityType, message: str, **kwargs) -> "ActivityLog":
        """Create warning log"""
        return cls.create_log(LogLevel.WARNING, activity_type, message, **kwargs)
    
    @classmethod
    def log_error(cls, activity_type: ActivityType, message: str, **kwargs) -> "ActivityLog":
        """Create error log"""
        return cls.create_log(LogLevel.ERROR, activity_type, message, **kwargs)
    
    @classmethod
    def log_critical(cls, activity_type: ActivityType, message: str, **kwargs) -> "ActivityLog":
        """Create critical log"""
        return cls.create_log(LogLevel.CRITICAL, activity_type, message, **kwargs)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "id": self.id,
            "level": self.level.value,
            "activity_type": self.activity_type.value,
            "message": self.message,
            "context": self.context_summary,
            "metadata": self.metadata or {},
            "error_info": {
                "error_code": self.error_code,
                "error_details": self.error_details,
                "stack_trace": self.stack_trace
            } if self.is_error else None,
            "performance": {
                "duration_ms": self.duration_ms,
                "memory_usage_mb": self.memory_usage_mb
            } if self.duration_ms or self.memory_usage_mb else None,
            "request_info": {
                "ip_address": self.ip_address,
                "user_agent": self.user_agent
            } if self.ip_address or self.user_agent else None,
            "created_at": self.created_at.isoformat(),
        }
    
    def __repr__(self):
        return f"<ActivityLog(id={self.id}, level={self.level.value}, type={self.activity_type.value}, message='{self.message[:50]}...')>"
