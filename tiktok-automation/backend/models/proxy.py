"""
Proxy model for managing different proxy types
"""

from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum

from sqlalchemy import Column, Integer, String, DateTime, <PERSON>olean, Enum as SQLEnum, Text
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid_property import hybrid_property

from core.database import Base


class ProxyType(str, Enum):
    """Supported proxy types"""
    HTTP = "http"
    HTTPS = "https"
    SOCKS4 = "socks4"
    SOCKS5 = "socks5"
    SSH = "ssh"


class ProxyStatus(str, Enum):
    """Proxy status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"


class Proxy(Base):
    """Proxy configuration model"""
    
    __tablename__ = "proxies"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic info
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Proxy configuration
    proxy_type = Column(SQLEnum(ProxyType), nullable=False, index=True)
    host = Column(String(255), nullable=False)
    port = Column(Integer, nullable=False)
    username = Column(String(255), nullable=True)
    password = Column(String(255), nullable=True)  # Should be encrypted
    
    # SSH specific (for SSH tunnels)
    ssh_private_key = Column(Text, nullable=True)  # Should be encrypted
    ssh_passphrase = Column(String(255), nullable=True)  # Should be encrypted
    
    # Status and health
    status = Column(SQLEnum(ProxyStatus), default=ProxyStatus.INACTIVE, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Performance metrics
    response_time_ms = Column(Integer, nullable=True)  # Average response time
    success_rate = Column(Integer, default=0, nullable=False)  # Percentage 0-100
    total_requests = Column(Integer, default=0, nullable=False)
    failed_requests = Column(Integer, default=0, nullable=False)
    
    # Geographic info (detected from IP)
    country = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv4 or IPv6
    
    # Health check
    last_checked = Column(DateTime, nullable=True)
    last_error = Column(Text, nullable=True)
    
    # Usage tracking
    usage_count = Column(Integer, default=0, nullable=False)
    last_used = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    browser_profiles = relationship("BrowserProfile", backref="proxy")
    
    @hybrid_property
    def proxy_url(self) -> str:
        """Generate proxy URL for use with requests/browsers"""
        if self.username and self.password:
            auth = f"{self.username}:{self.password}@"
        else:
            auth = ""
        
        return f"{self.proxy_type.value}://{auth}{self.host}:{self.port}"
    
    @hybrid_property
    def success_rate_percent(self) -> float:
        """Calculate success rate percentage"""
        if self.total_requests == 0:
            return 0.0
        return ((self.total_requests - self.failed_requests) / self.total_requests) * 100
    
    def get_proxy_dict(self) -> Dict[str, str]:
        """Get proxy configuration as dictionary for requests library"""
        proxy_url = self.proxy_url
        return {
            "http": proxy_url,
            "https": proxy_url
        }
    
    def get_camoufox_proxy_config(self) -> Dict[str, Any]:
        """Get proxy configuration for Camoufox"""
        config = {
            "server": f"{self.host}:{self.port}",
            "type": self.proxy_type.value
        }
        
        if self.username:
            config["username"] = self.username
        
        if self.password:
            config["password"] = self.password
        
        return config
    
    def update_stats(self, success: bool, response_time: Optional[int] = None):
        """Update proxy statistics"""
        self.total_requests += 1
        if not success:
            self.failed_requests += 1
        
        if response_time is not None:
            if self.response_time_ms is None:
                self.response_time_ms = response_time
            else:
                # Calculate moving average
                self.response_time_ms = int((self.response_time_ms + response_time) / 2)
        
        self.success_rate = int(self.success_rate_percent)
        self.last_used = datetime.utcnow()
    
    def update_health_check(self, success: bool, error_message: Optional[str] = None):
        """Update health check results"""
        self.last_checked = datetime.utcnow()
        
        if success:
            self.status = ProxyStatus.ACTIVE
            self.last_error = None
        else:
            self.status = ProxyStatus.ERROR
            self.last_error = error_message
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "proxy_type": self.proxy_type.value,
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "status": self.status.value,
            "is_active": self.is_active,
            "response_time_ms": self.response_time_ms,
            "success_rate": self.success_rate,
            "total_requests": self.total_requests,
            "failed_requests": self.failed_requests,
            "country": self.country,
            "city": self.city,
            "ip_address": self.ip_address,
            "last_checked": self.last_checked.isoformat() if self.last_checked else None,
            "last_error": self.last_error,
            "usage_count": self.usage_count,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
    
    def __repr__(self):
        return f"<Proxy(id={self.id}, name='{self.name}', type={self.proxy_type.value}, host='{self.host}:{self.port}')>"
